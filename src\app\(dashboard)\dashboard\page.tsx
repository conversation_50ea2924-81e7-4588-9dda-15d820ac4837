import { getUserAppointments } from '@/actions/appointment'
import {
  getUserBalance,
  getUserClients,
  getUserPlanInfo,
  getUserTotalProductPrices,
  getUserTransactions,
} from '@/actions/dashboard'
import DashboardCard from '@/components/dashboard/cards'
import { PlanUsage } from '@/components/dashboard/plan-usage'
import InfoBar from '@/components/infobar'
import { Separator } from '@/components/ui/separator'
import CalIcon from '@/icons/cal-icon'
import EmailIcon from '@/icons/email-icon'
import PersonIcon from '@/icons/person-icon'
import { TransactionsIcon } from '@/icons/transactions-icon'
import { DollarSign } from 'lucide-react'
import React from 'react'

type Props = {}

const Page = async (props: Props) => {
  const clients = await getUserClients()
  const sales = await getUserBalance()
  const bookings = await getUserAppointments()
  const plan = await getUserPlanInfo()
  const transactions = await getUserTransactions()
  const products = await getUserTotalProductPrices()

  // Ensure all values are properly defined
  const safeClients = clients ?? 0
  const safeSales = sales ?? 0
  const safeBookings = bookings ?? 0
  const safeProducts = products ?? 0
  const safePlan = plan ?? { plan: 'STANDARD', credits: 10, domains: 0 }
  const safeTransactions = transactions ?? { data: [] }

  return (
    <div className="dashboard-safe-zone prevent-masking">
      <InfoBar />
      <div className="overflow-y-auto w-full dashboard-content flex-1 h-0 p-4 sm:p-6">
        <div className="dashboard-grid prevent-masking">
          <DashboardCard
            value={safeClients}
            title="Potential Clients"
            icon={<PersonIcon />}
          />
          <DashboardCard
            value={safeProducts * safeClients}
            sales
            title="Pipeline Value"
            icon={<DollarSign />}
          />
          <DashboardCard
            value={safeBookings}
            title="Appointments"
            icon={<CalIcon />}
          />
          <DashboardCard
            value={safeSales}
            sales
            title="Total Sales"
            icon={<DollarSign />}
          />
        </div>
        <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
          <div className="space-y-6">
            <div>
              <h2 className="font-bold text-xl sm:text-2xl">Plan Usage</h2>
              <p className="text-sm sm:text-base font-light text-muted-foreground">
                A detailed overview of your metrics, usage, customers and more
              </p>
            </div>
            <PlanUsage
              plan={safePlan.plan}
              credits={safePlan.credits}
              domains={safePlan.domains}
              clients={safeClients}
            />
          </div>
          <div className="flex flex-col space-y-4">
            <div className="w-full flex justify-between items-start">
              <div className="flex gap-3 items-center">
                <TransactionsIcon />
                <p className="font-bold text-sm sm:text-base">Recent Transactions</p>
              </div>
              <p className="text-sm text-primary hover:underline cursor-pointer">See more</p>
            </div>
            <Separator orientation="horizontal" />
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {safeTransactions.data.length > 0 ? (
                safeTransactions.data.map((transaction) => (
                  <div
                    className="flex gap-3 w-full justify-between items-center border-b pb-4 last:border-b-0"
                    key={transaction.id}
                  >
                    <p className="font-medium text-sm sm:text-base truncate">
                      {transaction.calculated_statement_descriptor}
                    </p>
                    <p className="font-bold text-lg sm:text-xl text-green-600">
                      ${transaction.amount / 100}
                    </p>
                  </div>
                ))
              ) : (
                <p className="text-muted-foreground text-center py-8">No transactions yet</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Page
