#!/usr/bin/env node

/**
 * Environment Variables Validation Script
 * Validates all required environment variables for Corinna AI platform
 */

const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const REQUIRED_VARS = {
  // Database
  'DATABASE_URL': {
    required: true,
    description: 'Neon PostgreSQL connection string',
    example: '**********************************************'
  },
  
  // Clerk Authentication
  'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY': {
    required: true,
    description: 'Clerk publishable key',
    example: 'pk_test_...'
  },
  'CLERK_SECRET_KEY': {
    required: true,
    description: 'Clerk secret key',
    example: 'sk_test_...'
  },
  
  // OpenAI
  'OPEN_AI_KEY': {
    required: true,
    description: 'OpenAI API key for AI chatbot',
    example: 'sk-...'
  },
  
  // Stripe
  'STRIPE_SECRET': {
    required: true,
    description: 'Stripe secret key for payments',
    example: 'sk_test_...'
  },
  'NEXT_PUBLIC_STRIPE_PUBLISH_KEY': {
    required: true,
    description: 'Stripe publishable key',
    example: 'pk_test_...'
  },
  'STRIPE_ACCOUNT_ID': {
    required: false,
    description: 'Stripe account ID for connected accounts',
    example: 'acct_...'
  },
  
  // Email
  'NODE_MAILER_EMAIL': {
    required: true,
    description: 'Gmail address for sending emails',
    example: '<EMAIL>'
  },
  'NODE_MAILER_GMAIL_APP_PASSWORD': {
    required: true,
    description: 'Gmail app password (16 characters)',
    example: 'abcd efgh ijkl mnop'
  },
  
  // Pusher
  'NEXT_PUBLIC_PUSHER_APP_ID': {
    required: true,
    description: 'Pusher app ID for real-time chat',
    example: '123456'
  },
  'NEXT_PUBLIC_PUSHER_APP_KEY': {
    required: true,
    description: 'Pusher app key',
    example: 'abcdef123456'
  },
  'NEXT_PUBLIC_PUSHER_APP_SECRET': {
    required: true,
    description: 'Pusher app secret',
    example: 'abcdef123456'
  },
  
  // UploadCare
  'NEXT_PUBLIC_UPLOAD_CARE_PUBLIC_KEY': {
    required: true,
    description: 'UploadCare public key for file uploads',
    example: 'abcdef123456'
  },
  'UPLOAD_CARE_SECRET_KEY': {
    required: true,
    description: 'UploadCare secret key',
    example: 'abcdef123456'
  }
};

const OPTIONAL_VARS = {
  'CLOUDWAYS_POSTS_URL': 'WordPress posts API endpoint',
  'CLOUDWAYS_FEATURED_IMAGES_URL': 'WordPress media API endpoint',
  'CLOUDWAYS_USERS_URL': 'WordPress users API endpoint'
};

function validateEnvironment() {
  console.log('🔍 Validating Corinna AI Environment Configuration...\n');
  
  let hasErrors = false;
  let missingRequired = [];
  let missingOptional = [];
  
  // Check required variables
  for (const [varName, config] of Object.entries(REQUIRED_VARS)) {
    const value = process.env[varName];
    
    if (!value || value.includes('your_') || value.includes('_here')) {
      hasErrors = true;
      missingRequired.push({
        name: varName,
        description: config.description,
        example: config.example
      });
      console.log(`❌ ${varName}: Missing or placeholder value`);
    } else {
      console.log(`✅ ${varName}: Configured`);
    }
  }
  
  // Check optional variables
  for (const [varName, description] of Object.entries(OPTIONAL_VARS)) {
    const value = process.env[varName];
    
    if (!value || value.includes('your_') || value.includes('_here')) {
      missingOptional.push({ name: varName, description });
      console.log(`⚠️  ${varName}: Not configured (optional)`);
    } else {
      console.log(`✅ ${varName}: Configured`);
    }
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  
  if (hasErrors) {
    console.log('❌ VALIDATION FAILED - Missing required environment variables:\n');
    
    missingRequired.forEach(({ name, description, example }) => {
      console.log(`${name}:`);
      console.log(`  Description: ${description}`);
      console.log(`  Example: ${example}\n`);
    });
    
    console.log('Please update your .env file with the missing values.');
    console.log('See ENVIRONMENT_SETUP.md for detailed setup instructions.');
    process.exit(1);
  } else {
    console.log('✅ VALIDATION PASSED - All required environment variables are configured!');
    
    if (missingOptional.length > 0) {
      console.log('\n⚠️  Optional variables not configured:');
      missingOptional.forEach(({ name, description }) => {
        console.log(`  ${name}: ${description}`);
      });
    }
    
    console.log('\n🚀 Your Corinna AI platform is ready to run!');
  }
}

// Run validation
validateEnvironment();
