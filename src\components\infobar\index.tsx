import React from 'react'
import BreadCrumb from './bread-crumb'
import { Card } from '../ui/card'
import { Headphones, Star, Trash } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'

type Props = {}

const InfoBar = (props: Props) => {
  return (
    <div className="flex w-full justify-between items-center py-2 mb-4 sm:mb-6 lg:mb-8">
      <div className="flex-1 min-w-0">
        <BreadCrumb />
      </div>
      <div className="flex gap-2 sm:gap-3 items-center ml-4">
        <div className="hidden sm:block">
          <Card className="rounded-xl flex gap-2 sm:gap-3 py-2 sm:py-3 px-3 sm:px-4 text-ghost">
            <Trash className="w-4 h-4 sm:w-5 sm:h-5" />
            <Star className="w-4 h-4 sm:w-5 sm:h-5" />
          </Card>
        </div>
        <Avatar className="w-8 h-8 sm:w-10 sm:h-10">
          <AvatarFallback className="bg-orange text-white">
            <Headphones className="w-4 h-4 sm:w-5 sm:h-5" />
          </AvatarFallback>
        </Avatar>
        <Avatar className="w-8 h-8 sm:w-10 sm:h-10">
          <AvatarImage
            src="https://github.com/shadcn.png"
            alt="@shadcn"
          />
          <AvatarFallback>CN</AvatarFallback>
        </Avatar>
      </div>
    </div>
  )
}

export default InfoBar
