import React from 'react'

type Props = {
  title: string
  value: number
  icon: JSX.Element
  sales?: boolean
}

const DashboardCard = ({ icon, title, value, sales }: Props) => {
  return (
    <div className="rounded-lg flex flex-col gap-3 p-4 sm:p-6 border border-border bg-cream dark:bg-muted w-full">
      <div className="flex gap-3 items-center">
        <div className="flex-shrink-0">
          {icon}
        </div>
        <h2 className="font-bold text-sm sm:text-base lg:text-lg leading-tight">{title}</h2>
      </div>
      <p className="font-bold text-2xl sm:text-3xl lg:text-4xl">
        {sales && '$'}
        {value}
      </p>
    </div>
  )
}

export default DashboardCard
