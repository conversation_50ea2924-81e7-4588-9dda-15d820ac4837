import React from 'react'

type Props = {
  title: string
  value: number
  icon: JSX.Element
  sales?: boolean
}

const DashboardCard = ({ icon, title, value, sales }: Props) => {
  // Ensure value is always a valid number
  const displayValue = typeof value === 'number' && !isNaN(value) ? value : 0

  return (
    <div className="dashboard-card">
      <div className="flex gap-3 items-center">
        <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center">
          {icon}
        </div>
        <h2 className="font-bold text-sm sm:text-base lg:text-lg leading-tight flex-1">{title}</h2>
      </div>
      <p className="font-bold text-2xl sm:text-3xl lg:text-4xl text-primary">
        {sales && '$'}
        {displayValue.toLocaleString()}
      </p>
    </div>
  )
}

export default DashboardCard
