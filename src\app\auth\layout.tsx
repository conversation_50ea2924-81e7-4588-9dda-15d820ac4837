import { currentUser } from '@clerk/nextjs'
import Image from 'next/image'
import { redirect } from 'next/navigation'
import React from 'react'

type Props = {
  children: React.ReactNode
}

const Layout = async ({ children }: Props) => {
  const user = await currentUser()

  if (user) redirect('/')

  return (
    <div className="min-h-screen flex w-full">
      <div className="w-full max-w-md lg:max-w-lg xl:max-w-xl mx-auto lg:mx-0 lg:w-1/2 flex flex-col items-start p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-[120px] sm:max-w-[150px] mb-6">
          <Image
            src="/images/logo.png"
            alt="Corinna AI Logo"
            sizes="(max-width: 640px) 120px, 150px"
            className="w-full h-auto"
            width={150}
            height={50}
            priority
          />
        </div>
        <div className="w-full flex-1 flex flex-col justify-center">
          {children}
        </div>
      </div>
      <div className="hidden lg:flex lg:w-1/2 xl:w-3/5 relative bg-cream flex-col justify-center p-8 xl:p-12 overflow-hidden">
        <div className="max-w-lg xl:max-w-xl">
          <h2 className="text-gravel text-2xl xl:text-4xl font-bold mb-4">
            Hi, I’m your AI powered sales assistant, Corinna!
          </h2>
          <p className="text-iridium text-sm xl:text-base mb-8">
            Corinna is capable of capturing lead information without a form...{' '}
            <br />
            something never done before 😉
          </p>
        </div>
        <div className="absolute bottom-0 right-0 w-full h-2/3 overflow-hidden">
          <Image
            src="/images/app-ui.png"
            alt="Corinna AI Application Interface"
            loading="lazy"
            sizes="(min-width: 1024px) 800px, 600px"
            className="object-cover object-top w-full h-full"
            width={800}
            height={600}
          />
        </div>
      </div>
    </div>
  )
}

export default Layout
