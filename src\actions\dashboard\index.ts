'use server'

import { client } from '@/lib/prisma'
import { currentUser } from '@clerk/nextjs'
import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET!, {
  typescript: true,
  apiVersion: '2024-04-10',
})

export const getUserClients = async () => {
  try {
    const user = await currentUser()
    if (user) {
      const clients = await client.customer.count({
        where: {
          Domain: {
            User: {
              clerkId: user.id,
            },
          },
        },
      })
      return clients || 0
    }
    return 0
  } catch (error) {
    console.log('Error fetching user clients:', error)
    return 0
  }
}

export const getUserBalance = async () => {
  try {
    const user = await currentUser()
    if (user) {
      const connectedStripe = await client.user.findUnique({
        where: {
          clerkId: user.id,
        },
        select: {
          stripeId: true,
        },
      })

      if (connectedStripe?.stripeId) {
        const transactions = await stripe.balance.retrieve({
          stripeAccount: connectedStripe.stripeId,
        })

        if (transactions) {
          const sales = transactions.pending.reduce((total, next) => {
            return total + next.amount
          }, 0)

          return sales / 100
        }
      }
    }
    return 0
  } catch (error) {
    console.log('Error fetching user balance:', error)
    return 0
  }
}

export const getUserPlanInfo = async () => {
  try {
    const user = await currentUser()
    if (user) {
      const plan = await client.user.findUnique({
        where: {
          clerkId: user.id,
        },
        select: {
          _count: {
            select: {
              domains: true,
            },
          },
          subscription: {
            select: {
              plan: true,
              credits: true,
            },
          },
        },
      })

      return {
        plan: plan?.subscription?.plan || 'STANDARD',
        credits: plan?.subscription?.credits || 10,
        domains: plan?._count?.domains || 0,
      }
    }
    return {
      plan: 'STANDARD',
      credits: 10,
      domains: 0,
    }
  } catch (error) {
    console.log('Error fetching user plan info:', error)
    return {
      plan: 'STANDARD',
      credits: 10,
      domains: 0,
    }
  }
}

export const getUserTotalProductPrices = async () => {
  try {
    const user = await currentUser()
    if (user) {
      const products = await client.product.findMany({
        where: {
          Domain: {
            User: {
              clerkId: user.id,
            },
          },
        },
        select: {
          price: true,
        },
      })

      const total = products.reduce((total, next) => {
        return total + next.price
      }, 0)

      return total
    }
    return 0
  } catch (error) {
    console.log('Error fetching user product prices:', error)
    return 0
  }
}

export const getUserTransactions = async () => {
  try {
    const user = await currentUser()
    if (user) {
      const connectedStripe = await client.user.findUnique({
        where: {
          clerkId: user.id,
        },
        select: {
          stripeId: true,
        },
      })

      if (connectedStripe?.stripeId) {
        const transactions = await stripe.charges.list({
          stripeAccount: connectedStripe.stripeId,
        })
        return transactions
      }
    }
    return { data: [] }
  } catch (error) {
    console.log('Error fetching user transactions:', error)
    return { data: [] }
  }
}
