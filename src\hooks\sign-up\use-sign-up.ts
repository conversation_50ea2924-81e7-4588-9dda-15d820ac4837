'use client'
import { useToast } from '@/components/ui/use-toast'
import {
  UserRegistrationProps,
  UserRegistrationSchema,
} from '@/schemas/auth.schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useSignUp } from '@clerk/nextjs'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { onCompleteUserRegistration } from '@/actions/auth'

export const useSignUpForm = () => {
  const { toast } = useToast()
  const [loading, setLoading] = useState<boolean>(false)
  const { signUp, isLoaded, setActive } = useSignUp()
  const router = useRouter()
  const methods = useForm<UserRegistrationProps>({
    resolver: zodResolver(UserRegistrationSchema),
    defaultValues: {
      type: 'owner',
    },
    mode: 'onChange',
  })

  const onGenerateOTP = async (
    email: string,
    password: string,
    onNext: React.Dispatch<React.SetStateAction<number>>
  ) => {
    if (!isLoaded) return

    try {
      await signUp.create({
        emailAddress: email,
        password: password,
      })

      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' })

      onNext((prev) => prev + 1)
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.errors && error.errors[0] ? error.errors[0].longMessage : 'An error occurred during OTP generation.',
      })
    }
  }

  const onHandleSubmit = methods.handleSubmit(
    async (values: UserRegistrationProps) => {
      if (!isLoaded) {
        toast({
          title: 'Error',
          description: 'Authentication service is not ready. Please try again.',
        })
        return
      }

      try {
        setLoading(true)
        const completeSignUp = await signUp.attemptEmailAddressVerification({
          code: values.otp,
        })

        if (completeSignUp.status !== 'complete') {
          setLoading(false)
          toast({
            title: 'Error',
            description: 'Invalid OTP code. Please check your email and try again.',
          })
          return
        }

        if (!signUp.createdUserId) {
          setLoading(false)
          toast({
            title: 'Error',
            description: 'User ID not found. Please try signing up again.',
          })
          return
        }

        const registered = await onCompleteUserRegistration(
          values.fullname,
          signUp.createdUserId,
          values.type
        )

        if (registered?.status === 200 && registered.user) {
          await setActive({
            session: completeSignUp.createdSessionId,
          })

          toast({
            title: 'Success',
            description: 'Account created successfully! Welcome to Corinna AI.',
          })

          setLoading(false)
          router.push('/dashboard')
        } else {
          setLoading(false)
          toast({
            title: 'Error',
            description: 'Failed to create user account. Please try again.',
          })
        }
      } catch (error: any) {
        setLoading(false)
        console.error('Registration error:', error)

        let errorMessage = 'An error occurred during registration.'

        if (error.errors && error.errors[0]) {
          errorMessage = error.errors[0].longMessage || error.errors[0].message
        } else if (error.message) {
          errorMessage = error.message
        }

        toast({
          title: 'Error',
          description: errorMessage,
        })
      }
    }
  )
  return {
    methods,
    onHandleSubmit,
    onGenerateOTP,
    loading,
  }
}
