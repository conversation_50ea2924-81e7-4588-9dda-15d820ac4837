import But<PERSON><PERSON>and<PERSON> from '@/components/forms/sign-up/button-handlers'
import SignUpFormProvider from '@/components/forms/sign-up/form-provider'
import HighLightBar from '@/components/forms/sign-up/highlight-bar'
import RegistrationFormStep from '@/components/forms/sign-up/registration-step'
import { currentUser } from '@clerk/nextjs'
import { redirect } from 'next/navigation'

import React from 'react'

type Props = {}

const SignUp = async (props: Props) => {
  // Check if user is already authenticated
  const user = await currentUser()

  if (user) {
    // User is already authenticated, redirect to dashboard
    redirect('/dashboard')
  }

  return (
    <div className="flex-1 py-8 sm:py-12 md:py-16 lg:py-24 w-full">
      <div className="flex flex-col h-full gap-4 sm:gap-6">
        <SignUpFormProvider>
          <div className="flex flex-col gap-4 sm:gap-6">
            <RegistrationFormStep />
            <ButtonHandler />
          </div>
          <HighLightBar />
        </SignUpFormProvider>
      </div>
    </div>
  )
}

export default SignUp
