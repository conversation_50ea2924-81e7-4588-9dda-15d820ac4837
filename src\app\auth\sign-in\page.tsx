
import SignInFormProvider from '@/components/forms/sign-in/form-provider'
import LoginForm from '@/components/forms/sign-in/login-form'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import React from 'react'

const SignInPage = () => {
  return (
    <div className="flex-1 py-8 sm:py-12 md:py-16 lg:py-24 w-full">
      <div className="flex flex-col h-full gap-4 sm:gap-6">
        <SignInFormProvider>
          <div className="flex flex-col gap-4 sm:gap-6">
            <LoginForm />
            <div className="w-full flex flex-col gap-4 items-center">
              <Button
                type="submit"
                className="w-full min-h-[44px]"
              >
                Submit
              </Button>
              <p className="text-sm sm:text-base text-center">
                Don’t have an account?{' '}
                <Link
                  href="/auth/sign-up"
                  className="font-bold text-primary hover:underline"
                >
                  Create one
                </Link>
              </p>
            </div>
          </div>
        </SignInFormProvider>
      </div>
    </div>
  )
}

export default SignInPage
