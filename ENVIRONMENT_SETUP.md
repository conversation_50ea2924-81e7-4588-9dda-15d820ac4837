# 🔧 Corinna AI Platform - Environment Setup Guide

## Overview
This guide will help you configure all required environment variables for the Corinna AI platform to function properly.

## ✅ Current Status
- **Database**: ✅ Configured (Neon PostgreSQL)
- **Authentication**: ✅ Configured (Clerk)
- **OpenAI**: ❌ Required
- **Stripe**: ❌ Required  
- **Email**: ❌ Required
- **Pusher**: ❌ Required
- **UploadCare**: ❌ Required
- **WordPress**: ⚠️ Optional

---

## 🚀 Quick Setup

### 1. OpenAI Configuration (Required for AI Chatbot)
```bash
# Get API key from: https://platform.openai.com/api-keys
OPEN_AI_KEY=sk-your_openai_api_key_here
```

### 2. Stripe Configuration (Required for Payments)
```bash
# Get keys from: https://dashboard.stripe.com/apikeys
STRIPE_SECRET=sk_test_your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISH_KEY=pk_test_your_stripe_publishable_key_here
```

### 3. Email Configuration (Required for Notifications)
```bash
# Gmail SMTP setup
NODE_MAILER_EMAIL=<EMAIL>
NODE_MAILER_GMAIL_APP_PASSWORD=your_16_character_app_password
```

**Gmail Setup Steps:**
1. Enable 2-factor authentication
2. Go to Google Account settings
3. Generate App Password for "Mail"
4. Use the 16-character password

### 4. Pusher Configuration (Required for Real-time Chat)
```bash
# Get credentials from: https://dashboard.pusher.com/
NEXT_PUBLIC_PUSHER_APP_ID=your_pusher_app_id
NEXT_PUBLIC_PUSHER_APP_KEY=your_pusher_app_key
NEXT_PUBLIC_PUSHER_APP_SECRET=your_pusher_app_secret
NEXT_PUBLIC_PUSHER_APP_CLUSTOR=us2
```

### 5. UploadCare Configuration (Required for File Uploads)
```bash
# Get keys from: https://uploadcare.com/dashboard/
NEXT_PUBLIC_UPLOAD_CARE_PUBLIC_KEY=your_uploadcare_public_key
UPLOAD_CARE_SECRET_KEY=your_uploadcare_secret_key
```

---

## 🔍 Detailed Service Setup

### OpenAI Setup
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Create account or sign in
3. Go to API Keys section
4. Create new secret key
5. Copy and add to `.env` file

### Stripe Setup
1. Visit [Stripe Dashboard](https://dashboard.stripe.com/)
2. Create account or sign in
3. Go to Developers > API Keys
4. Copy Publishable key and Secret key
5. Add both keys to `.env` file

### Pusher Setup
1. Visit [Pusher Dashboard](https://dashboard.pusher.com/)
2. Create account or sign in
3. Create new app
4. Go to App Keys section
5. Copy all credentials to `.env` file

### UploadCare Setup
1. Visit [UploadCare Dashboard](https://uploadcare.com/dashboard/)
2. Create account or sign in
3. Go to API Keys section
4. Copy Public and Secret keys
5. Add both keys to `.env` file

---

## 🧪 Testing Configuration

After setting up all environment variables, test the platform:

1. **Start the application:**
   ```bash
   npm run dev
   ```

2. **Test features:**
   - ✅ User registration/login (Clerk)
   - ✅ Database operations (Neon PostgreSQL)
   - 🧪 AI Chatbot (OpenAI)
   - 🧪 Payment processing (Stripe)
   - 🧪 Email notifications (NodeMailer)
   - 🧪 Real-time chat (Pusher)
   - 🧪 File uploads (UploadCare)

---

## ⚠️ Security Notes

- Never commit `.env` file to version control
- Use different keys for development and production
- Regularly rotate API keys
- Monitor usage and billing for all services
- Use environment-specific configurations

---

## 🆘 Troubleshooting

### Common Issues:
1. **OpenAI API errors**: Check API key validity and billing
2. **Stripe connection failed**: Verify both keys are correct
3. **Email not sending**: Check Gmail app password setup
4. **Real-time chat not working**: Verify Pusher cluster setting
5. **File upload fails**: Check UploadCare key configuration

### Getting Help:
- Check service dashboards for status
- Verify API key permissions
- Review error logs in browser console
- Test individual services separately
