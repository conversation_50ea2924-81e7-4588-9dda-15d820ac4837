import { onLoginUser } from '@/actions/auth'
import SideBar from '@/components/sidebar'
import { ChatProvider } from '@/context/user-chat-context'
import React from 'react'

type Props = {
  children: React.ReactNode
}

const OwnerLayout = async ({ children }: Props) => {
  const authenticated = await onLoginUser()
  if (!authenticated) return null

  return (
    <ChatProvider>
      <div className="flex h-screen w-full overflow-hidden">
        <SideBar domains={authenticated.domain} />
        <div className="flex-1 h-screen flex flex-col overflow-hidden pl-0 md:pl-16 lg:pl-4">
          <main className="flex-1 overflow-auto p-4 md:p-6">
            {children}
          </main>
        </div>
      </div>
    </ChatProvider>
  )
}

export default OwnerLayout
