# =============================================================================
# CORINNA AI PLATFORM - ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and replace all placeholder values with your actual credentials
# Never commit .env file to version control - it contains sensitive information

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Neon PostgreSQL Database
# Get your connection string from: https://console.neon.tech/
DATABASE_URL="************************************************************"

# =============================================================================
# CLERK AUTHENTICATION
# =============================================================================
# Get your keys from: https://dashboard.clerk.com/
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_secret_key_here
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/auth/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/auth/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/dashboard

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================
# Get your API key from: https://platform.openai.com/api-keys
OPEN_AI_KEY=sk-your_openai_api_key_here

# =============================================================================
# STRIPE PAYMENT PROCESSING
# =============================================================================
# Get your keys from: https://dashboard.stripe.com/apikeys
STRIPE_SECRET=sk_test_your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISH_KEY=pk_test_your_stripe_publishable_key_here

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# Gmail SMTP Configuration for sending emails
# 1. Enable 2-factor authentication on your Gmail account
# 2. Generate app password: https://support.google.com/accounts/answer/185833
NODE_MAILER_EMAIL=<EMAIL>
NODE_MAILER_GMAIL_APP_PASSWORD=your_16_character_app_password

# =============================================================================
# PUSHER REAL-TIME CHAT
# =============================================================================
# Get your credentials from: https://dashboard.pusher.com/
NEXT_PUBLIC_PUSHER_APP_ID=your_pusher_app_id
NEXT_PUBLIC_PUSHER_APP_KEY=your_pusher_app_key
NEXT_PUBLIC_PUSHER_APP_SECRET=your_pusher_app_secret
NEXT_PUBLIC_PUSHER_APP_CLUSTOR=us2

# =============================================================================
# UPLOADCARE FILE UPLOADS
# =============================================================================
# Get your keys from: https://uploadcare.com/dashboard/
NEXT_PUBLIC_UPLOAD_CARE_PUBLIC_KEY=your_uploadcare_public_key
UPLOAD_CARE_SECRET_KEY=your_uploadcare_secret_key

# =============================================================================
# WORDPRESS/CLOUDWAYS BLOG INTEGRATION (OPTIONAL)
# =============================================================================
# Configure only if you want to integrate with WordPress blog
CLOUDWAYS_POSTS_URL=https://your-wordpress-site.com/wp-json/wp/v2/posts
CLOUDWAYS_FEATURED_IMAGES_URL=https://your-wordpress-site.com/wp-json/wp/v2/media
CLOUDWAYS_UPLOADS_URL=https://your-wordpress-site.com/wp-json/wp/v2/uploads
CLOUDWAYS_USERS_URL=https://your-wordpress-site.com/wp-json/wp/v2/users/
